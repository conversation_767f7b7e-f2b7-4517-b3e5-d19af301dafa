# import os
# import requests
# import json
# import hashlib
# import hmac
# from flask import request, jsonify, Blueprint, abort, redirect
# from flask_jwt_extended import jwt_required, get_jwt_identity
# from bson import ObjectId, json_util
# import datetime

# transactions_bp = Blueprint('transactions_bp', __name__)

# PAYSTACK_SECRET_KEY = os.getenv('PAYSTACK_SECRET_KEY', 'YOUR_PAYSTACK_SECRET_KEY')
# PAYSTACK_API_URL = "https://api.paystack.co"

# @transactions_bp.route('/initiate', methods=['POST'])
# @jwt_required()
# def initiate_transaction():
#     """
#     Initiates a new transaction for a SINGLE product.
#     """
#     db = transactions_bp.db
#     current_user_id = ObjectId(get_jwt_identity())
#     data = request.get_json()
#     product_id = data.get('product_id')

#     if not product_id:
#         return jsonify({"msg": "Product ID is required"}), 400

#     product = db.products.find_one({"_id": ObjectId(product_id)})
#     if not product:
#         return jsonify({"msg": "Product not found"}), 404

#     business = db.businesses.find_one({"_id": product['business_id']})
#     if not business:
#         return jsonify({"msg": "Business associated with product not found"}), 404
        
#     user = db.users.find_one({"_id": current_user_id})
#     if not user:
#         return jsonify({"msg": "Buyer not found"}), 404

#     new_order = {
#         "buyer_id": current_user_id,
#         "seller_id": business['owner_id'],
#         "items": [{"product_id": ObjectId(product_id), "quantity": 1}],
#         "total_amount": product['price'],
#         "status": "pending",
#         "paystack_reference": None,
#         "created_at": datetime.datetime.now(datetime.timezone.utc),
#         "updated_at": datetime.datetime.now(datetime.timezone.utc)
#     }
    
#     order_result = db.orders.insert_one(new_order)
#     order_id = order_result.inserted_id

#     headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
#     amount_in_kobo = int(product['price'] * 100)

#     payload = {
#         "email": user['email'],
#         "amount": amount_in_kobo,
#         "metadata": { "order_id": str(order_id), "source": "single_purchase" }
#     }

#     try:
#         response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
#         response.raise_for_status()
#         paystack_data = response.json()

#         if paystack_data['status'] is True:
#             db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
#             return jsonify({"msg": "Transaction initiated", "authorization_url": paystack_data['data']['authorization_url']}), 200
#         else:
#             return jsonify({"msg": "Failed to initialize payment with Paystack"}), 500

#     except requests.exceptions.RequestException as e:
#         return jsonify({"msg": "Could not connect to payment gateway", "error": str(e)}), 503
#     except Exception as e:
#         return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500

# @transactions_bp.route('/initiate-cart-checkout', methods=['POST'])
# @jwt_required()
# def initiate_cart_checkout():
#     """
#     Initiates a transaction for all items in the user's cart.
#     """
#     db = transactions_bp.db
#     current_user_id = ObjectId(get_jwt_identity())

#     cart = db.carts.find_one({'user_id': current_user_id})
#     if not cart or not cart.get('items'):
#         return jsonify({"msg": "Your cart is empty"}), 400

#     total_amount = 0
#     order_items = []
#     for item in cart['items']:
#         product = db.products.find_one({"_id": ObjectId(item['product_id'])})
#         if product:
#             total_amount += product['price'] * item['quantity']
#             order_items.append({
#                 "product_id": item['product_id'],
#                 "quantity": item['quantity'],
#                 "price_at_purchase": product['price']
#             })

#     if total_amount == 0:
#         return jsonify({"msg": "No valid items in cart to checkout"}), 400

#     user = db.users.find_one({"_id": current_user_id})

#     new_order = {
#         "buyer_id": current_user_id,
#         "items": order_items,
#         "total_amount": total_amount,
#         "status": "pending",
#         "paystack_reference": None,
#         "created_at": datetime.datetime.now(datetime.timezone.utc),
#         "updated_at": datetime.datetime.now(datetime.timezone.utc)
#     }
#     order_result = db.orders.insert_one(new_order)
#     order_id = order_result.inserted_id

#     headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
#     amount_in_kobo = int(total_amount * 100)
#     payload = {
#         "email": user['email'],
#         "amount": amount_in_kobo,
#         "metadata": { "order_id": str(order_id), "source": "cart_checkout" }
#     }

#     try:
#         response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
#         response.raise_for_status()
#         paystack_data = response.json()

#         if paystack_data.get('status'):
#             db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
#             return jsonify({"authorization_url": paystack_data['data']['authorization_url']}), 200
#         else:
#             return jsonify({"msg": "Failed to initialize cart payment"}), 500
#     except Exception as e:
#         return jsonify({"msg": "An error occurred during cart checkout", "error": str(e)}), 500

# @transactions_bp.route('/webhook', methods=['GET', 'POST'])
# def paystack_webhook():
#     """
#     Listens for and processes incoming webhooks from Paystack.
#     Also handles the client-side redirect after payment.
#     """
#     # This block handles the secure, server-to-server POST request from Paystack
#     if request.method == 'POST':
#         db = transactions_bp.db
        
#         signature = request.headers.get('x-paystack-signature')
#         if not signature:
#             abort(400)

#         hash = hmac.new(PAYSTACK_SECRET_KEY.encode('utf-8'), request.data, hashlib.sha512).hexdigest()
#         if hash != signature:
#             abort(401)

#         event_data = request.get_json()
#         event_type = event_data.get('event')

#         if event_type == 'charge.success':
#             metadata = event_data['data']['metadata']
#             order_id = metadata.get('order_id')
#             source = metadata.get('source')
            
#             if order_id:
#                 order = db.orders.find_one_and_update(
#                     {"_id": ObjectId(order_id)},
#                     {
#                         "$set": {
#                             "status": "paid",
#                             "updated_at": datetime.datetime.now(datetime.timezone.utc)
#                         }
#                     }
#                 )
#                 print(f"Order {order_id} status updated to 'paid'.")

#                 if source == 'cart_checkout' and order:
#                     buyer_id = order.get('buyer_id')
#                     db.carts.delete_one({"user_id": ObjectId(buyer_id)})
#                     print(f"Cleared cart for user {buyer_id}.")

#         return jsonify({"status": "success"}), 200
    
#     # THE FIX: This block handles the GET request from the user's WebView redirect.
#     # Its only job is to return a simple success message. The actual app logic
#     # is handled by the WebView listener in payment_webview_screen.dart.
#     else: # This handles the GET request
#         return "<h1>Payment Successful!</h1><p>You can now return to the Wicker app.</p>"

import os
import requests
import json
import hashlib
import hmac
from flask import request, jsonify, Blueprint, abort
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

transactions_bp = Blueprint('transactions_bp', __name__)

PAYSTACK_SECRET_KEY = os.getenv('PAYSTACK_SECRET_KEY', 'YOUR_PAYSTACK_SECRET_KEY')
PAYSTACK_API_URL = "https://api.paystack.co"
# NEW: Define the base URL for the backend to construct the callback URL
# BACKEND_BASE_URL = os.getenv('BACKEND_BASE_URL', 'YOUR_NGROK_OR_DEPLOYED_URL')
BACKEND_BASE_URL = "https://d5778b57eab7.ngrok-free.app"


@transactions_bp.route('/initiate', methods=['POST'])
@jwt_required()
def initiate_transaction():
    """
    Initiates a new transaction for a SINGLE product.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    product_id = data.get('product_id')

    if not product_id:
        return jsonify({"msg": "Product ID is required"}), 400

    product = db.products.find_one({"_id": ObjectId(product_id)})
    if not product:
        return jsonify({"msg": "Product not found"}), 404

    business = db.businesses.find_one({"_id": product['business_id']})
    if not business:
        return jsonify({"msg": "Business associated with product not found"}), 404
        
    user = db.users.find_one({"_id": current_user_id})
    if not user:
        return jsonify({"msg": "Buyer not found"}), 404

    new_order = {
        "buyer_id": current_user_id,
        "seller_id": business['owner_id'],
        "items": [{"product_id": ObjectId(product_id), "quantity": 1}],
        "total_amount": product['price'],
        "status": "pending",
        "paystack_reference": None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "updated_at": datetime.datetime.now(datetime.timezone.utc)
    }
    
    order_result = db.orders.insert_one(new_order)
    order_id = order_result.inserted_id

    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
    amount_in_kobo = int(product['price'] * 100)

    payload = {
        "email": user['email'],
        "amount": amount_in_kobo,
        "callback_url": f"{BACKEND_BASE_URL}/api/transactions/webhook", # THE FIX
        "metadata": { "order_id": str(order_id), "source": "single_purchase" }
    }

    try:
        response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        paystack_data = response.json()

        if paystack_data['status'] is True:
            db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
            return jsonify({"msg": "Transaction initiated", "authorization_url": paystack_data['data']['authorization_url']}), 200
        else:
            return jsonify({"msg": "Failed to initialize payment with Paystack"}), 500

    except requests.exceptions.RequestException as e:
        return jsonify({"msg": "Could not connect to payment gateway", "error": str(e)}), 503
    except Exception as e:
        return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500

@transactions_bp.route('/initiate-cart-checkout', methods=['POST'])
@jwt_required()
def initiate_cart_checkout():
    """
    Initiates a transaction for all items in the user's cart.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    cart = db.carts.find_one({'user_id': current_user_id})
    if not cart or not cart.get('items'):
        return jsonify({"msg": "Your cart is empty"}), 400

    total_amount = 0
    order_items = []
    for item in cart['items']:
        product = db.products.find_one({"_id": ObjectId(item['product_id'])})
        if product:
            total_amount += product['price'] * item['quantity']
            order_items.append({
                "product_id": item['product_id'],
                "quantity": item['quantity'],
                "price_at_purchase": product['price']
            })

    if total_amount == 0:
        return jsonify({"msg": "No valid items in cart to checkout"}), 400

    user = db.users.find_one({"_id": current_user_id})

    new_order = {
        "buyer_id": current_user_id,
        "items": order_items,
        "total_amount": total_amount,
        "status": "pending",
        "paystack_reference": None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "updated_at": datetime.datetime.now(datetime.timezone.utc)
    }
    order_result = db.orders.insert_one(new_order)
    order_id = order_result.inserted_id

    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
    amount_in_kobo = int(total_amount * 100)
    payload = {
        "email": user['email'],
        "amount": amount_in_kobo,
        "callback_url": f"{BACKEND_BASE_URL}/api/transactions/webhook", # THE FIX
        "metadata": { "order_id": str(order_id), "source": "cart_checkout" }
    }

    try:
        response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        paystack_data = response.json()

        if paystack_data.get('status'):
            db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
            return jsonify({"authorization_url": paystack_data['data']['authorization_url']}), 200
        else:
            return jsonify({"msg": "Failed to initialize cart payment"}), 500
    except Exception as e:
        return jsonify({"msg": "An error occurred during cart checkout", "error": str(e)}), 500


@transactions_bp.route('/webhook', methods=['GET', 'POST'])
def paystack_webhook():
    if request.method == 'POST':
        db = transactions_bp.db
        
        signature = request.headers.get('x-paystack-signature')
        if not signature:
            abort(400)

        hash = hmac.new(PAYSTACK_SECRET_KEY.encode('utf-8'), request.data, hashlib.sha512).hexdigest()
        if hash != signature:
            abort(401)

        event_data = request.get_json()
        event_type = event_data.get('event')

        if event_type == 'charge.success':
            metadata = event_data['data']['metadata']
            order_id = metadata.get('order_id')
            source = metadata.get('source')
            
            if order_id:
                order = db.orders.find_one_and_update(
                    {"_id": ObjectId(order_id)},
                    {"$set": {"status": "paid", "updated_at": datetime.datetime.now(datetime.timezone.utc)}}
                )
                print(f"Order {order_id} status updated to 'paid'.")

                if source == 'cart_checkout' and order:
                    buyer_id = order.get('buyer_id')
                    result = db.carts.delete_one({"user_id": ObjectId(buyer_id)})
                    print(f"Cleared cart for user {buyer_id}. Deleted count: {result.deleted_count}")

        return jsonify({"status": "success"}), 200
    else:  # GET request - this is the redirect after successful payment
        # Extract the transaction reference from the URL parameters
        trxref = request.args.get('trxref')
        reference = request.args.get('reference')
        
        print(f"Payment redirect received - trxref: {trxref}, reference: {reference}")
        
        # Return a simple HTML page that will be detected by the WebView
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Payment Successful</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background-color: #f0f0f0;
                }
                .success {
                    background-color: #4CAF50;
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px auto;
                    max-width: 400px;
                }
            </style>
        </head>
        <body>
            <div class="success">
                <h1>✅ Payment Successful!</h1>
                <p>Your payment has been processed successfully.</p>
                <p>You will be redirected back to the app shortly...</p>
            </div>
            <script>
                // Auto-close after 2 seconds (backup in case WebView doesn't catch it)
                setTimeout(function() {
                    window.close();
                }, 2000);
            </script>
        </body>
        </html>
        '''