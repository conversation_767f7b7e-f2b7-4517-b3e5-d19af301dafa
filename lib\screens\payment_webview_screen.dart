// import 'package:flutter/material.dart';
// import 'package:webview_flutter/webview_flutter.dart';
// import 'package:wicker/services/config_service.dart';

// class PaymentWebViewScreen extends StatefulWidget {
//   final String authorizationUrl;

//   const PaymentWebViewScreen({super.key, required this.authorizationUrl});

//   @override
//   State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
// }

// class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
//   WebViewController? _controller;
//   bool _isPageLoading = true;
//   bool _hasRedirected = false; // Prevent multiple redirects

//   @override
//   void initState() {
//     super.initState();
//     _initializeWebView();
//   }

//   Future<void> _initializeWebView() async {
//     final baseUrl = await ConfigService.instance.getBaseUrl();
//     final callbackUrl = '$baseUrl/api/transactions/webhook';

//     final controller = WebViewController()
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color(0x00000000))
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onPageStarted: (String url) {
//             print('Page started loading: $url'); // Debug log

//             // Check if this is the success callback URL
//             if (!_hasRedirected && url.startsWith(callbackUrl)) {
//               print('Success callback detected!'); // Debug log
//               _hasRedirected = true;

//               // Automatically close and return success
//               Future.delayed(const Duration(milliseconds: 500), () {
//                 if (mounted) {
//                   Navigator.of(context).pop(true);
//                 }
//               });
//             }
//           },
//           onPageFinished: (String url) {
//             print('Page finished loading: $url'); // Debug log

//             if (mounted) {
//               setState(() {
//                 _isPageLoading = false;
//               });
//             }

//             // Double-check on page finish as well
//             if (!_hasRedirected && url.startsWith(callbackUrl)) {
//               print('Success callback detected on page finish!'); // Debug log
//               _hasRedirected = true;

//               Future.delayed(const Duration(milliseconds: 500), () {
//                 if (mounted) {
//                   Navigator.of(context).pop(true);
//                 }
//               });
//             }
//           },
//           onNavigationRequest: (NavigationRequest request) {
//             print('Navigation request: ${request.url}'); // Debug log

//             if (!_hasRedirected && request.url.startsWith(callbackUrl)) {
//               print('Success callback detected in navigation!'); // Debug log
//               _hasRedirected = true;

//               // Prevent the navigation and close with success
//               Navigator.of(context).pop(true);
//               return NavigationDecision.prevent;
//             }
//             return NavigationDecision.navigate;
//           },
//           onWebResourceError: (WebResourceError error) {
//             print('WebView error: ${error.description}'); // Debug log
//           },
//         ),
//       )
//       ..loadRequest(Uri.parse(widget.authorizationUrl));

//     if (mounted) {
//       setState(() {
//         _controller = controller;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Complete Your Payment'),
//         backgroundColor: Colors.white,
//         foregroundColor: Colors.black,
//         elevation: 1,
//         leading: IconButton(
//           icon: const Icon(Icons.close),
//           onPressed: () {
//             Navigator.of(
//               context,
//             ).pop(false); // Return false for cancelled payment
//           },
//         ),
//       ),
//       body: Stack(
//         children: [
//           if (_controller != null) WebViewWidget(controller: _controller!),
//           if (_isPageLoading || _controller == null)
//             const Center(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   CircularProgressIndicator(),
//                   SizedBox(height: 16),
//                   Text('Loading payment page...'),
//                 ],
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:wicker/services/config_service.dart';

class PaymentWebViewScreen extends StatefulWidget {
  final String authorizationUrl;

  const PaymentWebViewScreen({super.key, required this.authorizationUrl});

  @override
  State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
  WebViewController? _controller;
  bool _isPageLoading = true;
  bool _hasRedirected = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    final baseUrl = await ConfigService.instance.getBaseUrl();
    final callbackUrl = '$baseUrl/api/transactions/webhook';

    final controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..addJavaScriptChannel(
        'PaymentChannel',
        onMessageReceived: (JavaScriptMessage message) {
          print('JavaScript message received: ${message.message}');
          if (message.message == 'payment_success' && !_hasRedirected) {
            _hasRedirected = true;
            Navigator.of(context).pop(true);
          }
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('Page started loading: $url');

            // Check if URL contains the webhook path with query parameters
            if (!_hasRedirected &&
                url.contains('/api/transactions/webhook') &&
                (url.contains('trxref=') || url.contains('reference='))) {
              print('Success callback URL detected!');
              _hasRedirected = true;
              Navigator.of(context).pop(true);
            }
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');

            if (mounted) {
              setState(() {
                _isPageLoading = false;
              });
            }

            // Double-check on page finish
            if (!_hasRedirected &&
                url.contains('/api/transactions/webhook') &&
                (url.contains('trxref=') || url.contains('reference='))) {
              print('Success callback detected on page finish!');
              _hasRedirected = true;
              Navigator.of(context).pop(true);
            }

            // Inject JavaScript to auto-trigger success after a short delay
            if (!_hasRedirected && url.contains('/api/transactions/webhook')) {
              controller.runJavaScript('''
                setTimeout(function() {
                  if (window.PaymentChannel) {
                    window.PaymentChannel.postMessage('payment_success');
                  }
                }, 1000);
              ''');
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            print('Navigation request: ${request.url}');

            // Check if URL contains the webhook path
            if (!_hasRedirected &&
                request.url.contains('/api/transactions/webhook') &&
                (request.url.contains('trxref=') ||
                    request.url.contains('reference='))) {
              print('Success callback detected in navigation!');
              _hasRedirected = true;
              Navigator.of(context).pop(true);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.authorizationUrl));

    if (mounted) {
      setState(() {
        _controller = controller;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Payment'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop(false);
          },
        ),
      ),
      body: Stack(
        children: [
          if (_controller != null) WebViewWidget(controller: _controller!),
          if (_isPageLoading || _controller == null)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading payment page...'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
