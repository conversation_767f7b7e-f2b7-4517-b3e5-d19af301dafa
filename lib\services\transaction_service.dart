// import 'dart:convert';
// import 'package:wicker/services/config_service.dart';
// import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

// class TransactionService {
//   final WickerHttpClient _client = WickerHttpClient();
//   final ConfigService _config = ConfigService.instance;

//   /// Initiates a payment for a product and returns the secure Paystack URL.
//   ///
//   /// Takes a [productId] and calls the backend to create a pending order.
//   /// The backend then communicates with Paystack to get a one-time
//   /// payment URL for the user to complete the transaction.
//   Future<String> initiateTransaction(String productId) async {
//     final baseUrl = await _config.getBaseUrl();
//     try {
//       final response = await _client.post(
//         Uri.parse('$baseUrl/api/transactions/initiate'),
//         body: jsonEncode({'product_id': productId}),
//       );

//       if (response.statusCode == 200) {
//         final responseBody = jsonDecode(response.body);
//         // Extract the secure URL from the backend's response
//         return responseBody['authorization_url'];
//       } else {
//         // Handle errors from the backend
//         final responseBody = jsonDecode(response.body);
//         throw Exception(
//           responseBody['msg'] ?? 'Failed to initiate transaction',
//         );
//       }
//     } catch (e) {
//       print('Transaction service error: $e');
//       rethrow;
//     }
//   }
// }

import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class TransactionService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  /// Initiates a payment for a single product.
  Future<String> initiateTransaction(String productId) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/transactions/initiate'),
        body: jsonEncode({'product_id': productId}),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        return responseBody['authorization_url'];
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(
          responseBody['msg'] ?? 'Failed to initiate transaction',
        );
      }
    } catch (e) {
      print('Transaction service error: $e');
      rethrow;
    }
  }

  /// NEW: Initiates a payment for the entire cart.
  ///
  /// This method calls the backend to calculate the total price of the cart
  /// and returns a single, secure Paystack URL for the entire order.
  Future<String> initiateCartCheckout() async {
    final baseUrl = await _config.getBaseUrl();
    try {
      // This is a simple POST request with no body, as the backend
      // will identify the user and their cart via the JWT token.
      final response = await _client.post(
        Uri.parse('$baseUrl/api/transactions/initiate-cart-checkout'),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        return responseBody['authorization_url'];
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(
          responseBody['msg'] ?? 'Failed to initiate cart checkout',
        );
      }
    } catch (e) {
      print('Cart checkout service error: $e');
      rethrow;
    }
  }
}
